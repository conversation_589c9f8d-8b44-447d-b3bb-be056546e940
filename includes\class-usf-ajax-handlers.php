<?php

/**
 * AJAX handlers for the auction plugin
 *
 * Handles all AJAX requests from frontend and admin
 */

class USF_Ajax_Handlers {

    /**
     * Initialize AJAX handlers
     */
    public static function init() {
        // Public AJAX handlers (for logged in and non-logged in users)
        add_action('wp_ajax_usf_submit_bid', array(self::class, 'handle_submit_bid'));
        add_action('wp_ajax_nopriv_usf_submit_bid', array(self::class, 'handle_submit_bid'));
        
        add_action('wp_ajax_usf_get_lot_details', array(self::class, 'handle_get_lot_details'));
        add_action('wp_ajax_nopriv_usf_get_lot_details', array(self::class, 'handle_get_lot_details'));
        
        add_action('wp_ajax_usf_check_auction_status', array(self::class, 'handle_check_auction_status'));
        add_action('wp_ajax_nopriv_usf_check_auction_status', array(self::class, 'handle_check_auction_status'));
        
        add_action('wp_ajax_usf_get_time_remaining', array(self::class, 'handle_get_time_remaining'));
        add_action('wp_ajax_nopriv_usf_get_time_remaining', array(self::class, 'handle_get_time_remaining'));
        
        add_action('wp_ajax_usf_get_user_bid_status', array(self::class, 'handle_get_user_bid_status'));

        // Admin AJAX handlers (for logged in users with proper capabilities)
        add_action('wp_ajax_usf_upload_csv', array(self::class, 'handle_upload_csv'));
        add_action('wp_ajax_usf_accept_bid', array(self::class, 'handle_accept_bid'));
        add_action('wp_ajax_usf_reject_bid', array(self::class, 'handle_reject_bid'));
        add_action('wp_ajax_usf_bulk_bid_action', array(self::class, 'handle_bulk_bid_action'));
        add_action('wp_ajax_usf_delete_lot', array(self::class, 'handle_delete_lot'));
        add_action('wp_ajax_usf_update_lot_status', array(self::class, 'handle_update_lot_status'));
        add_action('wp_ajax_usf_update_lot_field', array(self::class, 'handle_update_lot_field'));
        add_action('wp_ajax_usf_send_test_email', array(self::class, 'handle_send_test_email'));
    }

    /**
     * Handle bid submission
     */
    public static function handle_submit_bid() {
        // Check if user is logged in
        if (!is_user_logged_in()) {
            wp_send_json_error(array(
                'message' => 'You must be logged in to place a bid',
                'errors' => array('Please log in to continue')
            ));
        }

        // Verify nonce
        if (!wp_verify_nonce($_POST['usf_bid_nonce'], 'usf_submit_bid')) {
            wp_die('Security check failed');
        }

        // Get current user data
        $current_user = wp_get_current_user();

        // Sanitize input data and use WordPress user data
        $bid_data = array(
            'lot_id' => sanitize_text_field($_POST['lot_id']),
            'user_id' => $current_user->ID,
            'user_name' => $current_user->display_name,
            'user_email' => $current_user->user_email,
            'user_phone' => get_user_meta($current_user->ID, 'phone', true), // Optional phone from user meta
            'bid_amount' => floatval($_POST['bid_amount'])
        );

        // Submit bid
        $result = USF_Bid_Manager::submit_bid($bid_data);

        if ($result['success']) {
            wp_send_json_success(array(
                'message' => $result['message'],
                'bid_id' => $result['bid_id'],
                'is_update' => $result['is_update']
            ));
        } else {
            wp_send_json_error(array(
                'message' => 'Failed to submit bid',
                'errors' => $result['errors']
            ));
        }
    }

    /**
     * Handle get lot details
     */
    public static function handle_get_lot_details() {
        // Verify nonce for security
        if (!wp_verify_nonce($_POST['nonce'] ?? '', 'usf_public_action')) {
            wp_send_json_error(array('message' => 'Security check failed'));
        }

        $lot_id = sanitize_text_field($_POST['lot_id']);
        
        if (empty($lot_id)) {
            wp_send_json_error(array('message' => 'Lot ID is required'));
        }

        $auction_details = USF_Auction_Manager::get_auction_details($lot_id);
        
        if (!$auction_details) {
            wp_send_json_error(array('message' => 'Auction lot not found'));
        }

        wp_send_json_success($auction_details);
    }

    /**
     * Handle check auction status
     */
    public static function handle_check_auction_status() {
        $lot_id = sanitize_text_field($_POST['lot_id']);
        
        if (empty($lot_id)) {
            wp_send_json_error(array('message' => 'Lot ID is required'));
        }

        $lot = USF_Database::get_lot($lot_id);
        if (!$lot) {
            wp_send_json_error(array('message' => 'Auction lot not found'));
        }

        $is_active = USF_Auction_Manager::is_auction_active($lot);
        $time_remaining = USF_Auction_Manager::get_time_remaining($lot->closing_time);

        wp_send_json_success(array(
            'is_active' => $is_active,
            'time_remaining' => $time_remaining,
            'status' => $lot->status
        ));
    }

    /**
     * Handle get time remaining
     */
    public static function handle_get_time_remaining() {
        $lot_id = sanitize_text_field($_POST['lot_id']);
        
        if (empty($lot_id)) {
            wp_send_json_error(array('message' => 'Lot ID is required'));
        }

        $lot = USF_Database::get_lot($lot_id);
        if (!$lot) {
            wp_send_json_error(array('message' => 'Auction lot not found'));
        }

        $time_remaining = USF_Auction_Manager::get_time_remaining($lot->closing_time);

        wp_send_json_success($time_remaining);
    }

    /**
     * Handle get user bid status
     */
    public static function handle_get_user_bid_status() {
        // Check if user is logged in
        if (!is_user_logged_in()) {
            wp_send_json_error(array('message' => 'User must be logged in'));
        }

        $lot_id = sanitize_text_field($_POST['lot_id']);
        
        if (empty($lot_id)) {
            wp_send_json_error(array('message' => 'Lot ID is required'));
        }

        $current_user = wp_get_current_user();
        $user_bid_status = USF_Database::get_user_bid_status($lot_id, $current_user->ID);
        $can_bid = USF_Database::user_can_bid($lot_id, $current_user->ID);

        $response = array(
            'has_bid' => !empty($user_bid_status),
            'can_bid' => $can_bid,
            'bid_status' => null
        );

        if ($user_bid_status) {
            $response['bid_status'] = array(
                'status' => $user_bid_status->status,
                'bid_amount' => floatval($user_bid_status->bid_amount),
                'bid_time' => $user_bid_status->bid_time,
                'admin_notes' => $user_bid_status->admin_notes
            );
        }

        wp_send_json_success($response);
    }

    /**
     * Handle CSV upload
     */
    public static function handle_upload_csv() {
        // Check user capabilities
        if (!current_user_can('manage_options')) {
            wp_send_json_error(array('message' => 'Insufficient permissions'));
        }

        // Verify nonce
        if (!wp_verify_nonce($_POST['nonce'], 'usf_upload_csv')) {
            wp_send_json_error(array('message' => 'Security check failed'));
        }

        // Check if auction house was selected
        if (empty($_POST['auction_house'])) {
            wp_send_json_error(array('message' => 'Please select an auction house format'));
        }

        $auction_house = sanitize_text_field($_POST['auction_house']);
        $valid_auction_houses = array('att', 'bell', 'tmobile');
        
        if (!in_array($auction_house, $valid_auction_houses)) {
            wp_send_json_error(array('message' => 'Invalid auction house selected'));
        }

        // Check if file was uploaded
        if (!isset($_FILES['csv_file']) || $_FILES['csv_file']['error'] !== UPLOAD_ERR_OK) {
            wp_send_json_error(array('message' => 'File upload failed'));
        }

        $file = $_FILES['csv_file'];

        // Validate file type
        $file_type = wp_check_filetype($file['name']);
        if ($file_type['ext'] !== 'csv') {
            wp_send_json_error(array('message' => 'Only CSV files are allowed'));
        }

        // Move uploaded file to temporary location
        $upload_dir = wp_upload_dir();
        $temp_file = $upload_dir['path'] . '/' . uniqid('auction_csv_') . '.csv';
        
        if (!move_uploaded_file($file['tmp_name'], $temp_file)) {
            wp_send_json_error(array('message' => 'Failed to save uploaded file'));
        }

        try {
            // Parse CSV file with manual auction house selection
            $parsed_data = USF_CSV_Parser::parse_csv($temp_file, $auction_house);
            
            if (isset($parsed_data['error'])) {
                unlink($temp_file);
                wp_send_json_error(array('message' => $parsed_data['error']));
            }

            // Import to database
            $import_result = USF_CSV_Parser::import_to_database($parsed_data, basename($file['name']));
            
            // Clean up temporary file
            unlink($temp_file);

            if (isset($import_result['error'])) {
                wp_send_json_error(array('message' => $import_result['error']));
            }

            wp_send_json_success(array(
                'message' => 'CSV imported successfully',
                'imported_count' => $import_result['imported_count'],
                'total_lots' => $import_result['total_lots'],
                'auction_house' => $auction_house,
                'errors' => $import_result['errors']
            ));

        } catch (Exception $e) {
            if (file_exists($temp_file)) {
                unlink($temp_file);
            }
            wp_send_json_error(array('message' => 'Import failed: ' . $e->getMessage()));
        }
    }

    /**
     * Handle accept bid
     */
    public static function handle_accept_bid() {
        // Check user capabilities
        if (!current_user_can('manage_options')) {
            wp_send_json_error(array('message' => 'Insufficient permissions'));
        }

        // Verify nonce
        if (!wp_verify_nonce($_POST['nonce'], 'usf_admin_action')) {
            wp_send_json_error(array('message' => 'Security check failed'));
        }

        $bid_id = intval($_POST['bid_id']);
        $admin_notes = sanitize_textarea_field($_POST['admin_notes']);

        if (empty($bid_id)) {
            wp_send_json_error(array('message' => 'Bid ID is required'));
        }

        $result = USF_Bid_Manager::accept_bid($bid_id, $admin_notes);

        if ($result['success']) {
            wp_send_json_success(array(
                'message' => $result['message'],
                'order_id' => isset($result['order_id']) ? $result['order_id'] : null
            ));
        } else {
            wp_send_json_error(array('message' => $result['message']));
        }
    }

    /**
     * Handle reject bid
     */
    public static function handle_reject_bid() {
        // Check user capabilities
        if (!current_user_can('manage_options')) {
            wp_send_json_error(array('message' => 'Insufficient permissions'));
        }

        // Verify nonce
        if (!wp_verify_nonce($_POST['nonce'], 'usf_admin_action')) {
            wp_send_json_error(array('message' => 'Security check failed'));
        }

        $bid_id = intval($_POST['bid_id']);
        $admin_notes = sanitize_textarea_field($_POST['admin_notes']);

        if (empty($bid_id)) {
            wp_send_json_error(array('message' => 'Bid ID is required'));
        }

        $result = USF_Bid_Manager::reject_bid($bid_id, $admin_notes);

        if ($result['success']) {
            wp_send_json_success(array('message' => $result['message']));
        } else {
            wp_send_json_error(array('message' => $result['message']));
        }
    }

    /**
     * Handle bulk bid action
     */
    public static function handle_bulk_bid_action() {
        // Check user capabilities
        if (!current_user_can('manage_options')) {
            wp_send_json_error(array('message' => 'Insufficient permissions'));
        }

        // Verify nonce
        if (!wp_verify_nonce($_POST['nonce'], 'usf_admin_action')) {
            wp_send_json_error(array('message' => 'Security check failed'));
        }

        $action = sanitize_text_field($_POST['action_type']);
        $bid_ids = isset($_POST['bid_ids']) && is_array($_POST['bid_ids']) ? array_map('intval', $_POST['bid_ids']) : array();
        $admin_notes = sanitize_textarea_field($_POST['admin_notes']);

        if (empty($action) || empty($bid_ids)) {
            wp_send_json_error(array('message' => 'Action and bid IDs are required'));
        }

        $result = USF_Bid_Manager::bulk_action($action, $bid_ids, $admin_notes);

        if ($result['success']) {
            wp_send_json_success(array(
                'message' => 'Bulk action completed',
                'results' => $result['results']
            ));
        } else {
            wp_send_json_error(array('message' => $result['message']));
        }
    }

    /**
     * Handle delete lot
     */
    public static function handle_delete_lot() {
        // Check user capabilities
        if (!current_user_can('manage_options')) {
            wp_send_json_error(array('message' => 'Insufficient permissions'));
        }

        // Verify nonce
        if (!wp_verify_nonce($_POST['nonce'], 'usf_admin_action')) {
            wp_send_json_error(array('message' => 'Security check failed'));
        }

        $lot_id = sanitize_text_field($_POST['lot_id']);

        if (empty($lot_id)) {
            wp_send_json_error(array('message' => 'Lot ID is required'));
        }

        $result = USF_Auction_Manager::delete_auction($lot_id);

        if ($result) {
            wp_send_json_success(array('message' => 'Auction lot deleted successfully'));
        } else {
            wp_send_json_error(array('message' => 'Failed to delete auction lot'));
        }
    }

    /**
     * Handle update lot status
     */
    public static function handle_update_lot_status() {
        // Check user capabilities
        if (!current_user_can('manage_options')) {
            wp_send_json_error(array('message' => 'Insufficient permissions'));
        }

        // Verify nonce
        if (!wp_verify_nonce($_POST['nonce'], 'usf_admin_action')) {
            wp_send_json_error(array('message' => 'Security check failed'));
        }

        $lot_id = sanitize_text_field($_POST['lot_id']);
        $status = sanitize_text_field($_POST['status']);

        if (empty($lot_id) || empty($status)) {
            wp_send_json_error(array('message' => 'Lot ID and status are required'));
        }

        $result = USF_Auction_Manager::update_auction_status($lot_id, $status);

        if ($result !== false) {
            wp_send_json_success(array('message' => 'Auction status updated successfully'));
        } else {
            wp_send_json_error(array('message' => 'Failed to update auction status'));
        }
    }

    /**
     * Handle update lot field (for inline editing)
     */
    public static function handle_update_lot_field() {
        // Check user capabilities
        if (!current_user_can('manage_options')) {
            wp_send_json_error(array('message' => 'Insufficient permissions'));
        }

        // Verify nonce
        if (!wp_verify_nonce($_POST['nonce'], 'usf_admin_action')) {
            wp_send_json_error(array('message' => 'Security check failed'));
        }

        $lot_id = sanitize_text_field($_POST['lot_id']);
        $field = sanitize_text_field($_POST['field']);
        $value = sanitize_text_field($_POST['value']);

        if (empty($lot_id) || empty($field)) {
            wp_send_json_error(array('message' => 'Lot ID and field are required'));
        }

        // Validate field name
        $allowed_fields = array('model', 'grade', 'total_units', 'min_offer', 'closing_time');
        if (!in_array($field, $allowed_fields)) {
            wp_send_json_error(array('message' => 'Invalid field name'));
        }

        // Validate and format value based on field type
        switch ($field) {
            case 'total_units':
                $value = intval($value);
                if ($value < 1) {
                    wp_send_json_error(array('message' => 'Total units must be a positive number'));
                }
                break;
            
            case 'min_offer':
                $value = floatval($value);
                if ($value < 0) {
                    wp_send_json_error(array('message' => 'Minimum offer must be a positive number'));
                }
                break;
            
            case 'closing_time':
                // Validate datetime format
                $timestamp = strtotime($value);
                if ($timestamp === false) {
                    wp_send_json_error(array('message' => 'Invalid date/time format'));
                }
                $value = date('Y-m-d H:i:s', $timestamp);
                break;
            
            case 'model':
            case 'grade':
                if (empty($value)) {
                    wp_send_json_error(array('message' => ucfirst($field) . ' cannot be empty'));
                }
                break;
        }

        global $wpdb;
        $table_lots = $wpdb->prefix . 'auction_lots';

        $result = $wpdb->update(
            $table_lots,
            array($field => $value),
            array('lot_id' => $lot_id),
            array('%s'),
            array('%s')
        );

        if ($result !== false) {
            // Format the response value for display
            $display_value = $value;
            if ($field === 'min_offer') {
                $display_value = '$' . number_format($value, 2);
            } elseif ($field === 'closing_time') {
                $display_value = date('M j, Y g:i A', strtotime($value));
            }

            wp_send_json_success(array(
                'message' => ucfirst(str_replace('_', ' ', $field)) . ' updated successfully',
                'display_value' => $display_value
            ));
        } else {
            wp_send_json_error(array('message' => 'Failed to update ' . str_replace('_', ' ', $field)));
        }
    }

    /**
     * Handle send test email
     */
    public static function handle_send_test_email() {
        // Check user capabilities
        if (!current_user_can('manage_options')) {
            wp_send_json_error(array('message' => 'Insufficient permissions'));
        }

        // Verify nonce
        if (!wp_verify_nonce($_POST['nonce'], 'usf_admin_action')) {
            wp_send_json_error(array('message' => 'Security check failed'));
        }

        $email_type = sanitize_text_field($_POST['email_type']);
        $recipient_email = sanitize_email($_POST['recipient_email']);

        if (empty($email_type) || empty($recipient_email)) {
            wp_send_json_error(array('message' => 'Email type and recipient are required'));
        }

        $result = USF_Email_Manager::send_test_email($email_type, $recipient_email);

        if ($result) {
            // Update last test email time
            update_option('usf_auction_last_test_email', current_time('mysql'));
            
            wp_send_json_success(array('message' => 'Test email sent successfully'));
        } else {
            wp_send_json_error(array('message' => 'Failed to send test email'));
        }
    }

    /**
     * Handle get dashboard stats (for admin dashboard widgets)
     */
    public static function handle_get_dashboard_stats() {
        // Check user capabilities
        if (!current_user_can('manage_options')) {
            wp_send_json_error(array('message' => 'Insufficient permissions'));
        }

        $stats = USF_Database::get_dashboard_stats();
        $auction_stats = USF_Auction_Manager::get_auction_stats();
        $bid_stats = USF_Bid_Manager::get_bid_stats();

        wp_send_json_success(array(
            'dashboard' => $stats,
            'auctions' => $auction_stats,
            'bids' => $bid_stats
        ));
    }

    /**
     * Handle refresh auction timers (for real-time updates)
     */
    public static function handle_refresh_timers() {
        $lot_ids = array_map('sanitize_text_field', $_POST['lot_ids']);
        
        if (empty($lot_ids)) {
            wp_send_json_error(array('message' => 'No lot IDs provided'));
        }

        $timers = array();
        
        foreach ($lot_ids as $lot_id) {
            $lot = USF_Database::get_lot($lot_id);
            if ($lot) {
                $timers[$lot_id] = array(
                    'time_remaining' => USF_Auction_Manager::get_time_remaining($lot->closing_time),
                    'is_active' => USF_Auction_Manager::is_auction_active($lot)
                );
            }
        }

        wp_send_json_success($timers);
    }

    /**
     * Handle search auctions (for AJAX search)
     */
    public static function handle_search_auctions() {
        $search_term = sanitize_text_field($_POST['search_term']);
        $filters = array();
        
        if (isset($_POST['auction_house'])) {
            $filters['auction_house'] = sanitize_text_field($_POST['auction_house']);
        }
        
        if (isset($_POST['limit'])) {
            $filters['limit'] = intval($_POST['limit']);
        }
        
        if (isset($_POST['offset'])) {
            $filters['offset'] = intval($_POST['offset']);
        }

        $auctions = USF_Auction_Manager::search_auctions($search_term, $filters);
        $total_count = USF_Database::get_lots_count(array(
            'search' => $search_term,
            'auction_house' => isset($filters['auction_house']) ? $filters['auction_house'] : '',
            'status' => 'active'
        ));

        wp_send_json_success(array(
            'auctions' => $auctions,
            'total_count' => $total_count
        ));
    }

    /**
     * Handle validate bid amount
     */
    public static function handle_validate_bid() {
        $lot_id = sanitize_text_field($_POST['lot_id']);
        $bid_amount = floatval($_POST['bid_amount']);
        
        if (empty($lot_id)) {
            wp_send_json_error(array('message' => 'Lot ID is required'));
        }

        $lot = USF_Database::get_lot($lot_id);
        if (!$lot) {
            wp_send_json_error(array('message' => 'Auction lot not found'));
        }

        $is_valid = $bid_amount >= $lot->min_offer;
        $is_active = USF_Auction_Manager::is_auction_active($lot);

        wp_send_json_success(array(
            'is_valid' => $is_valid,
            'is_active' => $is_active,
            'min_offer' => $lot->min_offer,
            'message' => $is_valid ? 'Bid amount is valid' : 'Bid amount must be at least $' . number_format($lot->min_offer, 2)
        ));
    }
}
