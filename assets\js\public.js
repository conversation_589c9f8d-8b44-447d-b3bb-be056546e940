/**
 * Public-facing JavaScript for USF Auction Plugin
 */

(function($) {
    'use strict';

    // Initialize when document is ready
    $(document).ready(function() {
        initCountdownTimers();
        initBidForm();
        initQuickBid();
        initAuctionSearch();
        
        // Update timers every second for real-time countdown
        setInterval(updateCountdownTimers, 1000);
    });

    /**
     * Initialize countdown timers
     */
    function initCountdownTimers() {
        $('.usf-countdown, .usf-countdown-large').each(function() {
            var $timer = $(this);
            var closingTime = $timer.data('closing-time');
            
            if (closingTime) {
                updateTimer($timer, closingTime);
            }
        });
    }

    /**
     * Update countdown timers
     */
    function updateCountdownTimers() {
        $('.usf-countdown, .usf-countdown-large').each(function() {
            var $timer = $(this);
            var closingTime = $timer.data('closing-time');
            
            if (closingTime) {
                updateTimer($timer, closingTime);
            }
        });
    }

    /**
     * Update individual timer
     */
    function updateTimer($timer, closingTime) {
        var now = new Date().getTime();
        var endTime = new Date(closingTime).getTime();
        var timeLeft = endTime - now;

        if (timeLeft <= 0) {
            // Auction has ended
            if ($timer.hasClass('usf-countdown-large')) {
                $timer.html('<h3 class="usf-expired">Auction Closed</h3>');
            } else {
                $timer.html('<span class="usf-timer-expired">Auction Closed</span>');
            }
            
            // Disable bid forms and buttons
            var lotId = $timer.closest('.usf-auction-card, .usf-single-auction').data('lot-id');
            if (lotId) {
                disableAuctionInteraction(lotId);
            }
            
            return;
        }

        // Calculate time components
        var days = Math.floor(timeLeft / (1000 * 60 * 60 * 24));
        var hours = Math.floor((timeLeft % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
        var minutes = Math.floor((timeLeft % (1000 * 60 * 60)) / (1000 * 60));
        var seconds = Math.floor((timeLeft % (1000 * 60)) / 1000);

        // Add urgency styling for auctions ending soon
        var totalHours = Math.floor(timeLeft / (1000 * 60 * 60));
        if (totalHours <= 1) {
            $timer.addClass('usf-countdown-urgent');
        } else {
            $timer.removeClass('usf-countdown-urgent');
        }

        // Format numbers with leading zeros for better visual consistency
        var formattedDays = String(days).padStart(2, '0');
        var formattedHours = String(hours).padStart(2, '0');
        var formattedMinutes = String(minutes).padStart(2, '0');
        var formattedSeconds = String(seconds).padStart(2, '0');

        // Update display with smooth transitions
        if ($timer.hasClass('usf-countdown-large')) {
            // Use correct selectors that match the template structure
            updateNumberWithTransition($timer.find('.usf-countdown-item.usf-days .usf-number'), formattedDays);
            updateNumberWithTransition($timer.find('.usf-countdown-item.usf-hours .usf-number'), formattedHours);
            updateNumberWithTransition($timer.find('.usf-countdown-item.usf-minutes .usf-number'), formattedMinutes);
            updateNumberWithTransition($timer.find('.usf-countdown-item.usf-seconds .usf-number'), formattedSeconds);
        } else {
            // For smaller countdown timers, update the entire element
            updateNumberWithTransition($timer.find('.usf-countdown-item.usf-days .usf-number'), formattedDays);
            updateNumberWithTransition($timer.find('.usf-countdown-item.usf-hours .usf-number'), formattedHours);
            updateNumberWithTransition($timer.find('.usf-countdown-item.usf-minutes .usf-number'), formattedMinutes);
            updateNumberWithTransition($timer.find('.usf-countdown-item.usf-seconds .usf-number'), formattedSeconds);
        }
    }

    /**
     * Update number with smooth transition effect
     */
    function updateNumberWithTransition($element, newValue) {
        if ($element.length === 0) return;
        
        var currentValue = $element.text();
        if (currentValue !== newValue) {
            $element.fadeOut(150, function() {
                $element.text(newValue).fadeIn(150);
            });
        }
    }

    /**
     * Disable auction interaction when expired
     */
    function disableAuctionInteraction(lotId) {
        var $card = $('.usf-auction-card[data-lot-id="' + lotId + '"]');
        var $single = $('.usf-single-auction[data-lot-id="' + lotId + '"]');
        
        // Disable buttons
        $card.find('.usf-btn').prop('disabled', true).addClass('usf-auction-closed').text('Auction Closed');
        $single.find('.usf-bid-form').hide();
        $single.find('.usf-bid-form-container').append('<p class="usf-auction-closed">This auction has closed.</p>');
    }

    /**
     * Initialize bid form
     */
    function initBidForm() {
        $(document).on('submit', '.usf-bid-form', function(e) {
            e.preventDefault();
            
            var $form = $(this);
            var $messages = $form.find('.usf-bid-messages');
            var $submitBtn = $form.find('button[type="submit"]');
            
            // Validate form
            if (!validateBidForm($form)) {
                return;
            }
            
            // Show loading state
            $submitBtn.prop('disabled', true).html('<span class="usf-spinner"></span> Submitting...');
            $messages.empty();
            
            // Prepare form data
            var formData = {
                action: 'usf_submit_bid',
                lot_id: $form.data('lot-id'),
                bid_amount: $form.find('#bid_amount').val(),
                usf_bid_nonce: $form.find('[name="usf_bid_nonce"]').val()
            };
            
            // Submit bid
            $.post(usf_ajax.ajax_url, formData)
                .done(function(response) {
                    if (response.success) {
                        showMessage($messages, 'success', response.data.message);
                        
                        if (response.data.is_update) {
                            showMessage($messages, 'info', 'Your previous bid has been updated.');
                        }
                        
                        // Reset form and refresh page to show updated status
                        $form[0].reset();
                        
                        // Refresh page after 2 seconds to show updated bid status
                        setTimeout(function() {
                            location.reload();
                        }, 2000);
                        
                    } else {
                        var errorMsg = response.data.message || 'Failed to submit bid';
                        if (response.data.errors && response.data.errors.length > 0) {
                            errorMsg += ': ' + response.data.errors.join(', ');
                        }
                        showMessage($messages, 'error', errorMsg);
                    }
                })
                .fail(function() {
                    showMessage($messages, 'error', 'Network error. Please try again.');
                })
                .always(function() {
                    $submitBtn.prop('disabled', false).text('Submit Bid');
                });
        });
    }

    /**
     * Validate bid form
     */
    function validateBidForm($form) {
        var isValid = true;
        var $messages = $form.find('.usf-bid-messages');
        $messages.empty();
        
        // Check required fields
        $form.find('[required]').each(function() {
            var $field = $(this);
            if (!$field.val().trim()) {
                showMessage($messages, 'error', 'Please fill in all required fields.');
                $field.focus();
                isValid = false;
                return false;
            }
        });
        
        if (!isValid) return false;
        
        // Validate bid amount
        var bidAmount = parseFloat($form.find('#bid_amount').val());
        var minOffer = parseFloat($form.find('#bid_amount').attr('min'));
        
        if (isNaN(bidAmount) || bidAmount < minOffer) {
            showMessage($messages, 'error', 'Bid amount must be at least $' + minOffer.toFixed(2));
            $form.find('#bid_amount').focus();
            return false;
        }
        
        // Check terms acceptance
        if (!$form.find('#terms_accepted').is(':checked')) {
            showMessage($messages, 'error', 'Please accept the terms and conditions.');
            return false;
        }
        
        return true;
    }

    /**
     * Initialize quick bid functionality
     */
    function initQuickBid() {
        $(document).on('click', '.usf-quick-bid', function(e) {
            e.preventDefault();
            
            var $btn = $(this);
            var lotId = $btn.data('lot-id');
            
            // Create quick bid modal (simplified version)
            var modalHtml = '<div class="usf-quick-bid-modal">' +
                '<div class="usf-modal-content">' +
                '<h3>Quick Bid</h3>' +
                '<p>For detailed bidding, please visit the auction details page.</p>' +
                '<div class="usf-modal-actions">' +
                '<button class="usf-btn usf-btn-primary" onclick="window.location.href=\'' + 
                addQueryParam(window.location.href, 'lot_id', lotId) + '\'">Go to Details</button>' +
                '<button class="usf-btn usf-btn-secondary usf-close-modal">Cancel</button>' +
                '</div>' +
                '</div>' +
                '</div>';
            
            $('body').append(modalHtml);
            
            // Close modal functionality
            $(document).on('click', '.usf-close-modal, .usf-quick-bid-modal', function(e) {
                if (e.target === this) {
                    $('.usf-quick-bid-modal').remove();
                }
            });
        });
    }

    /**
     * Initialize auction search
     */
    function initAuctionSearch() {
        var searchTimeout;
        
        $(document).on('input', '.usf-filter-form input[name="auction_search"]', function() {
            clearTimeout(searchTimeout);
            var $input = $(this);
            
            searchTimeout = setTimeout(function() {
                // Auto-submit search form after 500ms delay
                $input.closest('form').submit();
            }, 500);
        });
    }

    /**
     * Show message in container
     */
    function showMessage($container, type, message) {
        var $message = $('<div class="usf-message ' + type + '">' + message + '</div>');
        $container.append($message);
        
        // Auto-hide success messages after 5 seconds
        if (type === 'success') {
            setTimeout(function() {
                $message.fadeOut();
            }, 5000);
        }
    }

    /**
     * Validate email format
     */
    function isValidEmail(email) {
        var emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        return emailRegex.test(email);
    }

    /**
     * Add query parameter to URL
     */
    function addQueryParam(url, param, value) {
        var separator = url.indexOf('?') !== -1 ? '&' : '?';
        return url + separator + param + '=' + encodeURIComponent(value);
    }

    /**
     * Format currency
     */
    function formatCurrency(amount) {
        return '$' + parseFloat(amount).toFixed(2).replace(/\d(?=(\d{3})+\.)/g, '$&,');
    }

    /**
     * Real-time bid validation
     */
    $(document).on('input', '#bid_amount', function() {
        var $input = $(this);
        var bidAmount = parseFloat($input.val());
        var minOffer = parseFloat($input.attr('min'));
        var $form = $input.closest('form');
        var $messages = $form.find('.usf-bid-messages');
        
        // Clear previous validation messages
        $messages.find('.usf-message.validation').remove();
        
        if (!isNaN(bidAmount) && bidAmount < minOffer) {
            showMessage($messages, 'error validation', 'Bid amount must be at least ' + formatCurrency(minOffer));
        }
    });


})(jQuery);
